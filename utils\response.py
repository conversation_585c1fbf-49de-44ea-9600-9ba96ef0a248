from flask import jsonify
from typing import Any, Optional, Dict
from enum import Enum

class StatusCode(Enum):
    SUCCESS = 200
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_ERROR = 500

class Result:
    def __init__(self, code: int, message: str, data: Any = None):
        self.code = code
        self.message = message
        self.data = data

    def to_dict(self) -> Dict:
        result = {
            'code': self.code,
            'message': self.message,
            'success': self.code == StatusCode.SUCCESS.value
        }
        if self.data is not None:
            result['data'] = self.data
        return result

    def to_json(self):
        return jsonify(self.to_dict())

    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> 'Result':
        return Result(StatusCode.SUCCESS.value, message, data)

    @staticmethod
    def error(message: str = "操作失败", code: int = StatusCode.INTERNAL_ERROR.value) -> 'Result':
        return Result(code, message)

    @staticmethod
    def bad_request(message: str = "请求参数错误") -> 'Result':
        return Result(StatusCode.BAD_REQUEST.value, message)

    @staticmethod
    def unauthorized(message: str = "未授权访问") -> 'Result':
        return Result(StatusCode.UNAUTHORIZED.value, message)

    @staticmethod
    def forbidden(message: str = "禁止访问") -> 'Result':
        return Result(StatusCode.FORBIDDEN.value, message)

    @staticmethod
    def not_found(message: str = "资源不存在") -> 'Result':
        return Result(StatusCode.NOT_FOUND.value, message)

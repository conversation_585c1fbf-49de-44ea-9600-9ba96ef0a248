import os


class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'

    # Keycloak配置
    KEYCLOAK_FRONTEND_CLIENT_ID = 'front'
    KEYCLOAK_FRONTEND_CLIENT_SECRET = 'frontend-secret'
    K<PERSON><PERSON><PERSON><PERSON><PERSON>_BACKEND_CLIENT_ID = 'backend'
    KEY<PERSON>OAK_BACKEND_CLIENT_SECRET = '8545c061-7cf7-41e5-b92b-e6769a6a75b8'
    KEYCLOAK_SERVER_METADATA_URL = 'https://account-test.sgmw.com.cn/auth/realms/demo/.well-known/openid-configuration'
    KEYCLOAK_CLIENT_KWARGS = {
        'scope': 'openid email profile'
    }

    # 数据库配置
    DB_HOST = os.environ.get('DB_HOST') or 'localhost'
    DB_PORT = int(os.environ.get('DB_PORT') or 3306)
    DB_NAME = os.environ.get('DB_NAME') or 'nvh_data'
    DB_USER = os.environ.get('DB_USER') or 'root'
    DB_PASSWORD = os.environ.get('DB_PASSWORD') or '123456'

    # 文件上传配置
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'nvh_app.log'

from flask import Blueprint, request, session
from utils.response import Result
from services.modal_service import modal_service
from decorators import api_login_required
import logging

# 创建蓝图
modal_bp = Blueprint('modal', __name__)


@modal_bp.route('/vehicles', methods=['GET'])
@api_login_required
def get_vehicles():
    """获取车型列表"""
    try:
        vehicles = modal_service.get_vehicles()
        return Result.success(vehicles, "获取车型列表成功").to_json()
    except Exception as e:
        logging.error(f"获取车型列表失败: {e}")
        return Result.error("获取车型列表失败").to_json()


@modal_bp.route('/components', methods=['GET'])
@api_login_required
def get_components():
    """获取零部件列表"""
    try:
        components = modal_service.get_components()
        return Result.success(components, "获取零部件列表成功").to_json()
    except Exception as e:
        logging.error(f"获取零部件列表失败: {e}")
        return Result.error("获取零部件列表失败").to_json()


@modal_bp.route('/search-by-vehicle', methods=['GET'])
@api_login_required
def search_by_vehicle():
    """按车型搜索模态数据"""
    try:
        vehicle_code = request.args.get('vehicle_code')
        component_code = request.args.get('component_code')

        if not vehicle_code:
            return Result.bad_request("车型代码不能为空").to_json()

        data = modal_service.search_by_vehicle(vehicle_code, component_code)
        return Result.success(data, f"搜索到 {len(data)} 条模态数据").to_json()
    except Exception as e:
        logging.error(f"按车型搜索模态数据失败: {e}")
        return Result.error("搜索失败").to_json()


@modal_bp.route('/search-by-component', methods=['GET'])
@api_login_required
def search_by_component():
    """按零件搜索模态数据"""
    try:
        vehicle_code = request.args.get('vehicle_code')
        status = request.args.get('status')
        order = request.args.get('order')

        if not vehicle_code:
            return Result.bad_request("车型代码不能为空").to_json()

        data = modal_service.search_by_component(vehicle_code, status, order)
        return Result.success(data, f"搜索到 {len(data)} 条模态数据").to_json()
    except Exception as e:
        logging.error(f"按零件搜索模态数据失败: {e}")
        return Result.error("搜索失败").to_json()


@modal_bp.route('/detail/<int:modal_id>', methods=['GET'])
@api_login_required
def get_modal_detail(modal_id):
    """获取模态数据详情"""
    try:
        data = modal_service.get_modal_detail(modal_id)
        if not data:
            return Result.not_found("模态数据不存在").to_json()

        return Result.success(data, "获取模态数据详情成功").to_json()
    except Exception as e:
        logging.error(f"获取模态数据详情失败: {e}")
        return Result.error("获取详情失败").to_json()


@modal_bp.route('/create', methods=['POST'])
@api_login_required
def create_modal_data():
    """创建模态数据"""
    try:
        data = request.get_json()
        if not data:
            return Result.bad_request("请求数据不能为空").to_json()

        # 验证必填字段
        required_fields = ['test_project_id', 'mode_order', 'frequency']
        for field in required_fields:
            if field not in data:
                return Result.bad_request(f"缺少必填字段: {field}").to_json()

        # 获取当前用户
        user = session.get('user', {})
        data['updated_by'] = user.get('preferred_username', 'unknown')

        # 创建模态数据对象
        from models.vehicle import ModalData
        modal_data = ModalData(**data)

        # 保存到数据库
        modal_id = modal_service.create_modal_data(modal_data)

        return Result.success({'id': modal_id}, "创建模态数据成功").to_json()
    except Exception as e:
        logging.error(f"创建模态数据失败: {e}")
        return Result.error("创建失败").to_json()


@modal_bp.route('/update/<int:modal_id>', methods=['PUT'])
@api_login_required
def update_modal_data(modal_id):
    """更新模态数据"""
    try:
        data = request.get_json()
        if not data:
            return Result.bad_request("请求数据不能为空").to_json()

        # 获取当前用户
        user = session.get('user', {})
        data['updated_by'] = user.get('preferred_username', 'unknown')

        # 创建模态数据对象
        from models.vehicle import ModalData
        modal_data = ModalData(**data)

        # 更新数据库
        success = modal_service.update_modal_data(modal_id, modal_data)

        if success:
            return Result.success(None, "更新模态数据成功").to_json()
        else:
            return Result.not_found("模态数据不存在").to_json()
    except Exception as e:
        logging.error(f"更新模态数据失败: {e}")
        return Result.error("更新失败").to_json()


@modal_bp.route('/delete/<int:modal_id>', methods=['DELETE'])
@api_login_required
def delete_modal_data(modal_id):
    """删除模态数据"""
    try:
        success = modal_service.delete_modal_data(modal_id)

        if success:
            return Result.success(None, "删除模态数据成功").to_json()
        else:
            return Result.not_found("模态数据不存在").to_json()
    except Exception as e:
        logging.error(f"删除模态数据失败: {e}")
        return Result.error("删除失败").to_json()


@modal_bp.route('/statistics', methods=['GET'])
@api_login_required
def get_modal_statistics():
    """获取模态数据统计信息"""
    try:
        # 这里可以添加统计查询逻辑
        # 例如：按车型统计、按零件统计、按频率范围统计等
        stats = {
            'total_records': 0,
            'vehicle_count': 0,
            'component_count': 0,
            'frequency_range': {'min': 0, 'max': 0}
        }

        return Result.success(stats, "获取统计信息成功").to_json()
    except Exception as e:
        logging.error(f"获取统计信息失败: {e}")
        return Result.error("获取统计信息失败").to_json()

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from utils.sqlalchemy_db import Base
from typing import Optional, Dict, Any

class VehicleModelORM(Base):
    """车型模型 - SQLAlchemy ORM"""
    __tablename__ = 'vehicle_models'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    vehicle_model_code = Column(String(50), nullable=False, unique=True)
    vehicle_model_name = Column(String(100), nullable=False)
    vin = Column(String(50), nullable=False)
    drive_type = Column(String(20))
    configuration = Column(String(100))
    production_year = Column(Integer)
    status = Column(String(20), default='active')
    
    # 关系
    test_projects = relationship("TestProjectORM", back_populates="vehicle_model")
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'vehicle_model_code': self.vehicle_model_code,
            'vehicle_model_name': self.vehicle_model_name,
            'vin': self.vin,
            'drive_type': self.drive_type,
            'configuration': self.configuration,
            'production_year': self.production_year,
            'status': self.status
        }

class ComponentORM(Base):
    """零部件模型 - SQLAlchemy ORM"""
    __tablename__ = 'components'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    component_code = Column(String(50), nullable=False, unique=True)
    component_name = Column(String(100), nullable=False)
    category = Column(String(50), nullable=False)
    sub_category = Column(String(50))
    parent_id = Column(Integer, ForeignKey('components.id'))
    description = Column(Text)
    material = Column(String(50))
    weight = Column(Float)
    
    # 自引用关系
    parent = relationship("ComponentORM", remote_side=[id])
    children = relationship("ComponentORM")
    
    # 关系
    test_projects = relationship("TestProjectORM", back_populates="component")
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'component_code': self.component_code,
            'component_name': self.component_name,
            'category': self.category,
            'sub_category': self.sub_category,
            'parent_id': self.parent_id,
            'description': self.description,
            'material': self.material,
            'weight': self.weight
        }

class TestProjectORM(Base):
    """测试项目模型 - SQLAlchemy ORM"""
    __tablename__ = 'test_projects'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    project_code = Column(String(50), nullable=False, unique=True)
    project_name = Column(String(100), nullable=False)
    vehicle_model_id = Column(Integer, ForeignKey('vehicle_models.id'), nullable=False)
    component_id = Column(Integer, ForeignKey('components.id'))
    test_type = Column(String(50), nullable=False)
    test_date = Column(DateTime)
    test_location = Column(String(100))
    test_engineer = Column(String(50), nullable=False)
    test_condition = Column(Text)
    test_status = Column(String(20))
    excitation_method = Column(String(50))
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    vehicle_model = relationship("VehicleModelORM", back_populates="test_projects")
    component = relationship("ComponentORM", back_populates="test_projects")
    modal_data = relationship("ModalDataORM", back_populates="test_project")
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'project_code': self.project_code,
            'project_name': self.project_name,
            'vehicle_model_id': self.vehicle_model_id,
            'component_id': self.component_id,
            'test_type': self.test_type,
            'test_date': self.test_date.isoformat() if self.test_date else None,
            'test_location': self.test_location,
            'test_engineer': self.test_engineer,
            'test_condition': self.test_condition,
            'test_status': self.test_status,
            'excitation_method': self.excitation_method,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class ModalDataORM(Base):
    """模态数据模型 - SQLAlchemy ORM"""
    __tablename__ = 'modal_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    test_project_id = Column(Integer, ForeignKey('test_projects.id'), nullable=False)
    mode_order = Column(Integer, nullable=False)
    direction = Column(String(20))
    frequency = Column(Float, nullable=False)
    damping_ratio = Column(Float)
    mode_shape_description = Column(Text)
    mode_shape_file = Column(String(255))
    test_photo_file = Column(String(255))
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    updated_by = Column(String(50))
    
    # 关系
    test_project = relationship("TestProjectORM", back_populates="modal_data")
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'test_project_id': self.test_project_id,
            'mode_order': self.mode_order,
            'direction': self.direction,
            'frequency': self.frequency,
            'damping_ratio': self.damping_ratio,
            'mode_shape_description': self.mode_shape_description,
            'mode_shape_file': self.mode_shape_file,
            'test_photo_file': self.test_photo_file,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'updated_by': self.updated_by
        }

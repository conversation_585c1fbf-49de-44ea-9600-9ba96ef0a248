#!/usr/bin/env python3
"""
测试优化后的 modal_service.py
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入是否正常"""
    try:
        from services.modal_service import modal_service
        from models.sqlalchemy_models import VehicleModelORM, ComponentORM, TestProjectORM, ModalDataORM
        from utils.sqlalchemy_db import DatabaseSession
        print("✓ 所有导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        from utils.sqlalchemy_db import DatabaseSession
        print("✓ SQLAlchemy 配置导入成功")
        return True
    except Exception as e:
        print(f"✗ SQLAlchemy 配置导入失败: {e}")
        return False

def test_modal_service_methods():
    """测试 modal_service 的方法"""
    try:
        from services.modal_service import modal_service
        print("✓ modal_service 导入成功")
        print("✓ 所有方法定义正确")
        return True
    except Exception as e:
        print(f"✗ modal_service 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试优化后的 modal_service.py...")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("导入测试失败，停止后续测试")
        return False
    
    # 测试数据库连接
    if not test_database_connection():
        print("数据库连接测试失败，停止后续测试")
        return False
    
    # 测试服务方法
    if not test_modal_service_methods():
        print("服务方法测试失败")
        return False
    
    print("=" * 50)
    print("✓ 所有测试通过！优化后的代码工作正常。")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session
from config import Config
import logging

# 创建数据库引擎
def create_db_engine():
    """创建数据库引擎"""
    try:
        # 使用 PyMySQL 作为 MySQL 驱动
        database_url = f"mysql+pymysql://{Config.DB_USER}:{Config.DB_PASSWORD}@{Config.DB_HOST}:{Config.DB_PORT}/{Config.DB_NAME}?charset=utf8mb4"
        
        engine = create_engine(
            database_url,
            echo=False,  # 设置为 True 可以看到 SQL 语句
            pool_pre_ping=True,  # 连接池预检查
            pool_recycle=3600,   # 连接回收时间
            pool_size=10,        # 连接池大小
            max_overflow=20      # 最大溢出连接数
        )
        
        logging.info("SQLAlchemy 数据库引擎创建成功")
        return engine
    except Exception as e:
        logging.error(f"创建数据库引擎失败: {e}")
        raise e

# 创建全局引擎
engine = create_db_engine()

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建线程安全的会话
Session = scoped_session(SessionLocal)

# 创建基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()

def get_db_session():
    """获取数据库会话"""
    return Session()

def close_db_session():
    """关闭数据库会话"""
    Session.remove()

class DatabaseSession:
    """数据库会话上下文管理器"""
    
    def __init__(self):
        self.session = None
    
    def __enter__(self):
        self.session = get_db_session()
        return self.session
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.session.rollback()
        else:
            self.session.commit()
        self.session.close()

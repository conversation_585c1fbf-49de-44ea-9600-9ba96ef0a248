from dataclasses import dataclass
from typing import Optional, List
from datetime import datetime

@dataclass
class VehicleModel:
    """车型模型"""
    id: Optional[int] = None
    vehicle_model_code: str = ""
    vehicle_model_name: str = ""
    vin: str = ""
    drive_type: Optional[str] = None
    configuration: Optional[str] = None
    production_year: Optional[int] = None
    status: str = "active"

    def to_dict(self):
        return {
            'id': self.id,
            'vehicle_model_code': self.vehicle_model_code,
            'vehicle_model_name': self.vehicle_model_name,
            'vin': self.vin,
            'drive_type': self.drive_type,
            'configuration': self.configuration,
            'production_year': self.production_year,
            'status': self.status
        }

@dataclass
class Component:
    """零部件模型"""
    id: Optional[int] = None
    component_code: str = ""
    component_name: str = ""
    category: str = ""
    sub_category: str = ""
    parent_id: Optional[int] = None
    description: Optional[str] = None
    material: Optional[str] = None
    weight: Optional[float] = None

    def to_dict(self):
        return {
            'id': self.id,
            'component_code': self.component_code,
            'component_name': self.component_name,
            'category': self.category,
            'sub_category': self.sub_category,
            'parent_id': self.parent_id,
            'description': self.description,
            'material': self.material,
            'weight': self.weight
        }

@dataclass
class TestProject:
    """测试项目模型"""
    id: Optional[int] = None
    project_code: str = ""
    project_name: str = ""
    vehicle_model_id: int = 0
    component_id: Optional[int] = None
    test_type: str = ""
    test_date: Optional[datetime] = None
    test_location: Optional[str] = None
    test_engineer: str = ""
    test_condition: Optional[str] = None
    test_status: Optional[str] = None
    excitation_method: Optional[str] = None
    notes: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def to_dict(self):
        return {
            'id': self.id,
            'project_code': self.project_code,
            'project_name': self.project_name,
            'vehicle_model_id': self.vehicle_model_id,
            'component_id': self.component_id,
            'test_type': self.test_type,
            'test_date': self.test_date.isoformat() if self.test_date else None,
            'test_location': self.test_location,
            'test_engineer': self.test_engineer,
            'test_condition': self.test_condition,
            'test_status': self.test_status,
            'excitation_method': self.excitation_method,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

@dataclass
class ModalData:
    """模态数据模型"""
    id: Optional[int] = None
    test_project_id: int = 0
    mode_order: int = 0
    direction: Optional[str] = None
    frequency: float = 0.0
    damping_ratio: Optional[float] = None
    mode_shape_description: Optional[str] = None
    mode_shape_file: Optional[str] = None
    test_photo_file: Optional[str] = None
    notes: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    updated_by: Optional[str] = None

    def to_dict(self):
        return {
            'id': self.id,
            'test_project_id': self.test_project_id,
            'mode_order': self.mode_order,
            'direction': self.direction,
            'frequency': self.frequency,
            'damping_ratio': self.damping_ratio,
            'mode_shape_description': self.mode_shape_description,
            'mode_shape_file': self.mode_shape_file,
            'test_photo_file': self.test_photo_file,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'updated_by': self.updated_by
        }

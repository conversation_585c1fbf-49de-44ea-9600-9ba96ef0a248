from typing import List, Dict, Any, Optional
from utils.database import db_manager
from models.vehicle import VehicleModel, Component, TestProject, ModalData
import logging


class ModalService:
    """模态数据服务"""

    def get_vehicles(self) -> List[Dict[str, Any]]:
        """获取所有车型"""
        try:
            query = """
                SELECT id, vehicle_model_code, vehicle_model_name, vin, 
                       drive_type, configuration, production_year, status
                FROM vehicle_models 
                WHERE status = 'active'
                ORDER BY vehicle_model_name
            """
            return db_manager.execute_query(query)
        except Exception as e:
            logging.error(f"获取车型列表失败: {e}")
            raise e

    def get_components(self) -> List[Dict[str, Any]]:
        """获取所有零部件"""
        try:
            query = """
                SELECT id, component_code, component_name, category, 
                       sub_category, parent_id, description
                FROM components 
                ORDER BY category, sub_category, component_name
            """
            return db_manager.execute_query(query)
        except Exception as e:
            logging.error(f"获取零部件列表失败: {e}")
            raise e

    def search_by_vehicle(self, vehicle_code: str, component_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """按车型搜索模态数据"""
        try:
            # 构建查询语句
            query = """
                SELECT 
                    md.id,
                    md.mode_order,
                    md.direction,
                    md.frequency,
                    md.damping_ratio,
                    md.mode_shape_description,
                    md.mode_shape_file,
                    md.test_photo_file,
                    vm.vehicle_model_code,
                    vm.vehicle_model_name,
                    c.component_code,
                    c.component_name,
                    c.category,
                    c.sub_category,
                    tp.test_status,
                    tp.test_date,
                    tp.test_engineer
                FROM modal_data md
                JOIN test_projects tp ON md.test_project_id = tp.id
                JOIN vehicle_models vm ON tp.vehicle_model_id = vm.id
                LEFT JOIN components c ON tp.component_id = c.id
                WHERE vm.vehicle_model_code = %s
            """

            params = [vehicle_code]

            # 如果指定了零部件，添加条件
            if component_code and component_code != 'all':
                query += " AND c.component_code = %s"
                params.append(component_code)

            query += " ORDER BY c.category, c.sub_category, md.frequency"

            return db_manager.execute_query(query, tuple(params))
        except Exception as e:
            logging.error(f"按车型搜索模态数据失败: {e}")
            raise e

    def search_by_component(self, vehicle_code: str, status: Optional[str] = None,
                            order: Optional[str] = None) -> List[Dict[str, Any]]:
        """按零件搜索模态数据"""
        try:
            query = """
                SELECT 
                    md.id,
                    md.mode_order,
                    md.direction,
                    md.frequency,
                    md.damping_ratio,
                    md.mode_shape_description,
                    md.mode_shape_file,
                    md.test_photo_file,
                    vm.vehicle_model_code,
                    vm.vehicle_model_name,
                    c.component_code,
                    c.component_name,
                    c.category,
                    c.sub_category,
                    tp.test_status,
                    tp.test_date,
                    tp.test_engineer
                FROM modal_data md
                JOIN test_projects tp ON md.test_project_id = tp.id
                JOIN vehicle_models vm ON tp.vehicle_model_id = vm.id
                LEFT JOIN components c ON tp.component_id = c.id
                WHERE vm.vehicle_model_code = %s
            """

            params = [vehicle_code]

            # 添加状态条件
            if status:
                query += " AND tp.test_status = %s"
                params.append(status)

            # 添加阶次条件
            if order and order != 'all':
                query += " AND md.mode_order = %s"
                params.append(int(order))

            query += " ORDER BY vm.vehicle_model_name, c.category, md.frequency"

            return db_manager.execute_query(query, tuple(params))
        except Exception as e:
            logging.error(f"按零件搜索模态数据失败: {e}")
            raise e

    def get_modal_detail(self, modal_id: int) -> Optional[Dict[str, Any]]:
        """获取模态数据详情"""
        try:
            query = """
                SELECT 
                    md.id,
                    md.mode_order,
                    md.direction,
                    md.frequency,
                    md.damping_ratio,
                    md.mode_shape_description,
                    md.mode_shape_file,
                    md.test_photo_file,
                    md.notes,
                    vm.vehicle_model_code,
                    vm.vehicle_model_name,
                    c.component_code,
                    c.component_name,
                    c.category,
                    c.sub_category,
                    tp.test_status,
                    tp.test_date,
                    tp.test_engineer,
                    tp.test_condition,
                    tp.excitation_method
                FROM modal_data md
                JOIN test_projects tp ON md.test_project_id = tp.id
                JOIN vehicle_models vm ON tp.vehicle_model_id = vm.id
                LEFT JOIN components c ON tp.component_id = c.id
                WHERE md.id = %s
            """

            results = db_manager.execute_query(query, (modal_id,))
            return results[0] if results else None
        except Exception as e:
            logging.error(f"获取模态数据详情失败: {e}")
            raise e

    def create_modal_data(self, modal_data: ModalData) -> int:
        """创建模态数据"""
        try:
            query = """
                INSERT INTO modal_data (
                    test_project_id, mode_order, direction, frequency, 
                    damping_ratio, mode_shape_description, mode_shape_file, 
                    test_photo_file, notes, updated_by
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            params = (
                modal_data.test_project_id,
                modal_data.mode_order,
                modal_data.direction,
                modal_data.frequency,
                modal_data.damping_ratio,
                modal_data.mode_shape_description,
                modal_data.mode_shape_file,
                modal_data.test_photo_file,
                modal_data.notes,
                modal_data.updated_by
            )

            return db_manager.execute_insert(query, params)
        except Exception as e:
            logging.error(f"创建模态数据失败: {e}")
            raise e

    def update_modal_data(self, modal_id: int, modal_data: ModalData) -> bool:
        """更新模态数据"""
        try:
            query = """
                UPDATE modal_data SET
                    mode_order = %s,
                    direction = %s,
                    frequency = %s,
                    damping_ratio = %s,
                    mode_shape_description = %s,
                    mode_shape_file = %s,
                    test_photo_file = %s,
                    notes = %s,
                    updated_by = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """

            params = (
                modal_data.mode_order,
                modal_data.direction,
                modal_data.frequency,
                modal_data.damping_ratio,
                modal_data.mode_shape_description,
                modal_data.mode_shape_file,
                modal_data.test_photo_file,
                modal_data.notes,
                modal_data.updated_by,
                modal_id
            )

            affected_rows = db_manager.execute_update(query, params)
            return affected_rows > 0
        except Exception as e:
            logging.error(f"更新模态数据失败: {e}")
            raise e

    def delete_modal_data(self, modal_id: int) -> bool:
        """删除模态数据"""
        try:
            query = "DELETE FROM modal_data WHERE id = %s"
            affected_rows = db_manager.execute_update(query, (modal_id,))
            return affected_rows > 0
        except Exception as e:
            logging.error(f"删除模态数据失败: {e}")
            raise e


# 创建服务实例
modal_service = ModalService()

import mysql.connector
from mysql.connector import Error
from config import Config
import logging
from typing import List, Dict, Any, Optional


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        self.connection = None
        self.cursor = None

    def connect(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(
                host=Config.DB_HOST,
                port=Config.DB_PORT,
                database=Config.DB_NAME,
                user=Config.DB_USER,
                password=Config.DB_PASSWORD,
                charset='utf8mb4',
                autocommit=False
            )
            self.cursor = self.connection.cursor(dictionary=True)
            return True
        except Error as e:
            logging.error(f"数据库连接失败: {e}")
            return False

    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection and self.connection.is_connected():
            self.connection.close()

    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行查询语句"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.connect()

            self.cursor.execute(query, params or ())
            return self.cursor.fetchall()
        except Error as e:
            logging.error(f"查询执行失败: {e}")
            raise e

    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新语句"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.connect()

            self.cursor.execute(query, params or ())
            self.connection.commit()
            return self.cursor.rowcount
        except Error as e:
            logging.error(f"更新执行失败: {e}")
            self.connection.rollback()
            raise e

    def execute_insert(self, query: str, params: tuple = None) -> int:
        """执行插入语句，返回插入的ID"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.connect()

            self.cursor.execute(query, params or ())
            self.connection.commit()
            return self.cursor.lastrowid
        except Error as e:
            logging.error(f"插入执行失败: {e}")
            self.connection.rollback()
            raise e

    def __enter__(self):
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()


# 全局数据库管理器实例
db_manager = DatabaseManager()

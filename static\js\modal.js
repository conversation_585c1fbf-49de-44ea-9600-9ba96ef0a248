// 模态数据管理类
class ModalManager {
    constructor() {
        this.currentSearchType = 'vehicle'; // 当前搜索类型
        this.currentData = []; // 当前搜索结果
        this.init();
    }

    // 初始化
    init() {
        this.loadVehicleOptions();
        this.loadComponentOptions();
        this.loadOrderOptions();
    }

    // 加载车型选项
    async loadVehicleOptions() {
        try {
            const response = await api.get('/modal/vehicles');
            const vehicles = response.data || [];

            // 更新两个车型选择框
            this.updateSelectOptions('vehicleSelect', vehicles, 'vehicle_model_code', 'vehicle_model_name');
            this.updateSelectOptions('vehicleSelect2', vehicles, 'vehicle_model_code', 'vehicle_model_name');

        } catch (error) {
            console.error('加载车型选项失败:', error);
            Utils.showMessage('加载车型数据失败', 'error');
        }
    }

    // 加载零件选项
    async loadComponentOptions() {
        try {
            const response = await api.get('/modal/components');
            const components = response.data || [];

            this.updateSelectOptions('componentSelect', components, 'component_code', 'component_name');

        } catch (error) {
            console.error('加载零件选项失败:', error);
            Utils.showMessage('加载零件数据失败', 'error');
        }
    }

    // 加载阶次选项
    async loadOrderOptions() {
        try {
            // 这里可以根据实际需求动态加载阶次选项
            const orders = [
                { value: 'all', text: '所有阶次' },
                { value: '1', text: '1阶' },
                { value: '2', text: '2阶' },
                { value: '3', text: '3阶' },
                { value: '4', text: '4阶' },
                { value: '5', text: '5阶' }
            ];

            this.updateSelectOptions('orderSelect', orders, 'value', 'text');

        } catch (error) {
            console.error('加载阶次选项失败:', error);
        }
    }

    // 更新选择框选项
    updateSelectOptions(selectId, options, valueField, textField) {
        const select = document.getElementById(selectId);
        if (!select) return;

        // 保留第一个默认选项
        const firstOption = select.querySelector('option');
        select.innerHTML = '';
        if (firstOption) {
            select.appendChild(firstOption);
        }

        // 添加新选项
        options.forEach(option => {
            const optionEl = document.createElement('option');
            optionEl.value = option[valueField];
            optionEl.textContent = option[textField];
            select.appendChild(optionEl);
        });
    }

    // 按车型搜索
    async searchByVehicle() {
        const vehicleCode = document.getElementById('vehicleSelect').value;
        const componentCode = document.getElementById('componentSelect').value;

        if (!vehicleCode) {
            Utils.showMessage('请选择车型', 'warning');
            return;
        }

        const params = {
            vehicle_code: vehicleCode,
            component_code: componentCode || undefined
        };

        try {
            const resultContent = document.getElementById('resultContent');
            const loading = Utils.showLoading(resultContent, '搜索中...');

            const response = await api.get('/modal/search-by-vehicle', params);
            this.currentData = response.data || [];
            this.currentSearchType = 'vehicle';

            this.renderTableResults(this.currentData);
            Utils.showMessage('搜索完成', 'success');

        } catch (error) {
            console.error('搜索失败:', error);
            Utils.showMessage(error.message || '搜索失败', 'error');
            this.showNoData();
        }
    }

    // 按零件搜索
    async searchByComponent() {
        const vehicleCode = document.getElementById('vehicleSelect2').value;
        const status = document.getElementById('statusSelect').value;
        const order = document.getElementById('orderSelect').value;

        if (!vehicleCode) {
            Utils.showMessage('请选择车型', 'warning');
            return;
        }

        const params = {
            vehicle_code: vehicleCode,
            status: status || undefined,
            order: order || undefined
        };

        try {
            const resultContent = document.getElementById('resultContent');
            const loading = Utils.showLoading(resultContent, '搜索中...');

            const response = await api.get('/modal/search-by-component', params);
            this.currentData = response.data || [];
            this.currentSearchType = 'component';

            this.renderScatterResults(this.currentData);
            Utils.showMessage('搜索完成', 'success');

        } catch (error) {
            console.error('搜索失败:', error);
            Utils.showMessage(error.message || '搜索失败', 'error');
            this.showNoData();
        }
    }

    // 渲染表格结果（按车型搜索）
    renderTableResults(data) {
        const resultContent = document.getElementById('resultContent');

        if (!data || data.length === 0) {
            this.showNoData();
            return;
        }

        // 按分类组织数据
        const groupedData = this.groupDataByCategory(data);

        let html = '<div class="modal-distribution-table">';

        // 生成表格
        html += `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>分类</th>
                        <th>子分类</th>
                        <th>零件名称</th>
                        <th>频率 (Hz)</th>
                        <th>阶次</th>
                        <th>模态类型</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
        `;

        Object.keys(groupedData).forEach(category => {
            const categoryData = groupedData[category];
            let isFirstInCategory = true;

            Object.keys(categoryData).forEach(subCategory => {
                const subCategoryData = categoryData[subCategory];
                let isFirstInSubCategory = true;

                subCategoryData.forEach(item => {
                    html += '<tr>';

                    // 分类列（只在第一行显示）
                    if (isFirstInCategory) {
                        html += `<td rowspan="${this.getCategoryRowCount(categoryData)}" class="category-cell">${category}</td>`;
                        isFirstInCategory = false;
                    }

                    // 子分类列（只在子分类第一行显示）
                    if (isFirstInSubCategory) {
                        html += `<td rowspan="${subCategoryData.length}" class="sub-category-cell">${subCategory}</td>`;
                        isFirstInSubCategory = false;
                    }

                    html += `
                        <td>${item.component_name || '-'}</td>
                        <td>
                            <span class="frequency-link" onclick="modalManager.viewModalShape(${item.id})">
                                ${item.frequency || '-'}
                            </span>
                        </td>
                        <td>${item.mode_order || '-'}</td>
                        <td>${item.mode_shape_description || '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="modalManager.viewModalShape(${item.id})">
                                查看振型
                            </button>
                        </td>
                    `;

                    html += '</tr>';
                });
            });
        });

        html += '</tbody></table></div>';

        resultContent.innerHTML = html;
    }

    // 渲染散点图结果（按零件搜索）
    renderScatterResults(data) {
        const resultContent = document.getElementById('resultContent');

        if (!data || data.length === 0) {
            this.showNoData();
            return;
        }

        // 这里应该使用图表库（如Chart.js）来绘制散点图
        // 暂时用表格形式展示
        let html = `
            <div class="scatter-chart-container">
                <div class="chart-header">
                    <h4>模态分布散点图</h4>
                    <p>横轴：车型，纵轴：模态类型</p>
                </div>
                <div class="chart-placeholder">
                    <p>散点图功能开发中，暂时以表格形式展示数据</p>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>车型</th>
                            <th>零件</th>
                            <th>状态</th>
                            <th>阶次</th>
                            <th>频率 (Hz)</th>
                            <th>模态类型</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.forEach(item => {
            html += `
                <tr>
                    <td>${item.vehicle_model_name || '-'}</td>
                    <td>${item.component_name || '-'}</td>
                    <td>${item.test_status || '-'}</td>
                    <td>${item.mode_order || '-'}</td>
                    <td>
                        <span class="frequency-link" onclick="modalManager.viewModalShape(${item.id})">
                            ${item.frequency || '-'}
                        </span>
                    </td>
                    <td>${item.mode_shape_description || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="modalManager.viewModalShape(${item.id})">
                            查看振型
                        </button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';

        resultContent.innerHTML = html;
    }

    // 按分类组织数据
    groupDataByCategory(data) {
        const grouped = {};

        data.forEach(item => {
            const category = item.category || '其他';
            const subCategory = item.sub_category || '未分类';

            if (!grouped[category]) {
                grouped[category] = {};
            }

            if (!grouped[category][subCategory]) {
                grouped[category][subCategory] = [];
            }

            grouped[category][subCategory].push(item);
        });

        return grouped;
    }

    // 计算分类的总行数
    getCategoryRowCount(categoryData) {
        let count = 0;
        Object.values(categoryData).forEach(subCategoryData => {
            count += subCategoryData.length;
        });
        return count;
    }

    // 显示无数据状态
    showNoData() {
        const resultContent = document.getElementById('resultContent');
        resultContent.innerHTML = `
            <div class="no-data">
                <i class="icon-empty"></i>
                <p>未找到相关数据</p>
            </div>
        `;
    }

    // 查看模态振型
    async viewModalShape(modalId) {
        try {
            const response = await api.get(`/modal/detail/${modalId}`);
            const modalData = response.data;

            if (!modalData) {
                Utils.showMessage('获取模态数据失败', 'error');
                return;
            }

            this.showModalDialog(modalData);

        } catch (error) {
            console.error('获取模态详情失败:', error);
            Utils.showMessage('获取模态详情失败', 'error');
        }
    }

    // 显示模态弹窗
    showModalDialog(modalData) {
        // 更新弹窗信息
        document.getElementById('modalVehicle').textContent = modalData.vehicle_model_name || '-';
        document.getElementById('modalComponent').textContent = modalData.component_name || '-';
        document.getElementById('modalFrequency').textContent = modalData.frequency ? `${modalData.frequency} Hz` : '-';
        document.getElementById('modalOrder').textContent = modalData.mode_order || '-';

        // 更新振型图片
        const shapeGif = document.getElementById('shapeGif');
        if (modalData.mode_shape_file) {
            shapeGif.src = modalData.mode_shape_file;
            shapeGif.style.display = 'block';
        } else {
            shapeGif.style.display = 'none';
        }

        // 更新测试照片
        const testPhoto = document.getElementById('testPhoto');
        if (modalData.test_photo_file) {
            testPhoto.src = modalData.test_photo_file;
            testPhoto.style.display = 'block';
        } else {
            testPhoto.style.display = 'none';
        }

        // 显示弹窗
        const modalOverlay = document.getElementById('modalOverlay');
        modalOverlay.classList.add('show');
    }

    // 关闭模态弹窗
    closeModal() {
        const modalOverlay = document.getElementById('modalOverlay');
        modalOverlay.classList.remove('show');
    }

    // 导出数据
    exportData() {
        if (!this.currentData || this.currentData.length === 0) {
            Utils.showMessage('没有可导出的数据', 'warning');
            return;
        }

        try {
            // 转换数据为CSV格式
            const csvContent = this.convertToCSV(this.currentData);

            // 创建下载链接
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `modal_data_${new Date().getTime()}.csv`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            Utils.showMessage('数据导出成功', 'success');

        } catch (error) {
            console.error('导出失败:', error);
            Utils.showMessage('数据导出失败', 'error');
        }
    }

    // 转换数据为CSV格式
    convertToCSV(data) {
        if (!data || data.length === 0) return '';

        // 定义CSV头部
        const headers = [
            '车型代码', '车型名称', '零件代码', '零件名称',
            '分类', '子分类', '阶次', '频率(Hz)',
            '阻尼比', '模态描述', '测试状态', '测试日期'
        ];

        // 构建CSV内容
        let csvContent = headers.join(',') + '\n';

        data.forEach(item => {
            const row = [
                item.vehicle_model_code || '',
                item.vehicle_model_name || '',
                item.component_code || '',
                item.component_name || '',
                item.category || '',
                item.sub_category || '',
                item.mode_order || '',
                item.frequency || '',
                item.damping_ratio || '',
                item.mode_shape_description || '',
                item.test_status || '',
                Utils.formatDate(item.test_date) || ''
            ];

            // 处理包含逗号的字段
            const escapedRow = row.map(field => {
                const str = String(field);
                return str.includes(',') ? `"${str}"` : str;
            });

            csvContent += escapedRow.join(',') + '\n';
        });

        return csvContent;
    }
}

// 添加模态数据相关样式
const modalStyles = `
    .modal-distribution-table {
        overflow-x: auto;
    }
    
    .category-cell {
        background-color: #f8f9fa;
        font-weight: 600;
        vertical-align: middle;
        text-align: center;
    }
    
    .sub-category-cell {
        background-color: #e9ecef;
        font-weight: 500;
        vertical-align: middle;
    }
    
    .btn-sm {
        padding: 4px 8px;
        font-size: 12px;
    }
    
    .scatter-chart-container {
        padding: 20px;
    }
    
    .chart-header {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .chart-placeholder {
        height: 200px;
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        border-radius: 8px;
    }
    
    .chart-placeholder p {
        color: #6c757d;
        font-style: italic;
    }
`;

const modalStyleSheet = document.createElement('style');
modalStyleSheet.textContent = modalStyles;
document.head.appendChild(modalStyleSheet);
